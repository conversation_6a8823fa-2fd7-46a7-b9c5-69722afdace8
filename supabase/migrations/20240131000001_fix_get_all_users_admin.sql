-- Fix get_all_users_admin RPC function
-- This migration fixes the RPC function to work properly with service role access

-- Drop and recreate the function with proper logic
-- Handle different possible signatures
DROP FUNCTION IF EXISTS get_all_users_admin();
DROP FUNCTION IF EXISTS get_all_users_admin() CASCADE;

CREATE OR R<PERSON>LACE FUNCTION get_all_users_admin()
RETURNS TABLE (
    id UUID,
    email TEXT,
    full_name TEXT,
    user_type TEXT,
    phone TEXT,
    city TEXT,
    is_active BOOLEAN,
    is_company BOOLEAN,
    company_name TEXT,
    ice_number TEXT,
    company_address TEXT,
    company_phone TEXT,
    company_city TEXT,
    company_email TEXT,
    tax_id TEXT,
    legal_form TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
DECLARE
    current_user_id UUID;
    current_user_type TEXT;
    is_service_role BOOLEAN := FALSE;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Check if this is a service role call (no user context)
    -- Service role calls have elevated privileges
    IF current_user_id IS NULL THEN
        -- This is likely a service role call, allow it
        is_service_role := TRUE;
    ELSE
        -- Check if current user is admin or manager
        SELECT user_type INTO current_user_type
        FROM users 
        WHERE id = current_user_id AND is_active = true;
        
        IF current_user_type NOT IN ('admin', 'manager') THEN
            RAISE EXCEPTION 'Access denied. Only admins and managers can access all users.';
        END IF;
    END IF;

    -- Return all users (bypasses RLS due to SECURITY DEFINER)
    RETURN QUERY
    SELECT
        u.id,
        u.email,
        u.full_name,
        u.user_type,
        u.phone,
        u.city,
        u.is_active,
        u.is_company,
        u.company_name,
        u.ice_number,
        u.company_address,
        u.company_phone,
        u.company_city,
        u.company_email,
        u.tax_id,
        u.legal_form,
        u.created_at,
        u.updated_at
    FROM users u
    ORDER BY u.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_all_users_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_users_admin() TO anon;

-- Add comment
COMMENT ON FUNCTION get_all_users_admin() IS 'Returns all users for admin/manager access or service role, bypassing RLS policies';
