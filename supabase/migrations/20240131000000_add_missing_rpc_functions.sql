-- Add missing RPC functions for user management
-- This migration fixes critical real-time synchronization issues in User Management

-- =============================================
-- USER MANAGEMENT RPC FUNCTIONS
-- =============================================

-- Function to get all users for admin/manager access (bypasses RLS)
CREATE OR REPLACE FUNCTION get_all_users_admin()
RETURNS TABLE (
    id UUID,
    email TEXT,
    full_name TEXT,
    user_type TEXT,
    phone TEXT,
    city TEXT,
    is_active BOOLEAN,
    status TEXT,
    is_company BOOLEAN,
    company_name TEXT,
    ice_number TEXT,
    company_address TEXT,
    company_phone TEXT,
    company_city TEXT,
    company_email TEXT,
    tax_id TEXT,
    legal_form TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Check if current user is admin or manager
    IF NOT is_manager_or_admin() THEN
        RAISE EXCEPTION 'Access denied. Only admins and managers can access all users.';
    END IF;

    -- Return all users (bypasses RLS due to SECURITY DEFINER)
    RETURN QUERY
    SELECT 
        u.id,
        u.email,
        u.full_name,
        u.user_type,
        u.phone,
        u.city,
        u.is_active,
        u.status,
        u.is_company,
        u.company_name,
        u.ice_number,
        u.company_address,
        u.company_phone,
        u.company_city,
        u.company_email,
        u.tax_id,
        u.legal_form,
        u.created_at,
        u.updated_at
    FROM users u
    ORDER BY u.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if email exists (for user registration)
CREATE OR REPLACE FUNCTION check_email_exists(email_to_check TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE email = email_to_check
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for admin password updates with proper authorization
CREATE OR REPLACE FUNCTION admin_update_user_password(
    target_user_email TEXT,
    admin_user_id UUID
)
RETURNS JSONB AS $$
DECLARE
    admin_user_type TEXT;
    target_user_exists BOOLEAN;
BEGIN
    -- Check if the admin user exists and is actually an admin
    SELECT user_type INTO admin_user_type
    FROM users 
    WHERE id = admin_user_id AND is_active = true;

    IF admin_user_type IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Admin user not found or inactive'
        );
    END IF;

    IF admin_user_type != 'admin' THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Only admin users can update passwords'
        );
    END IF;

    -- Check if target user exists
    SELECT EXISTS(SELECT 1 FROM users WHERE email = target_user_email) INTO target_user_exists;

    IF NOT target_user_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Target user not found'
        );
    END IF;

    -- Log the password update action (optional - you can add audit logging here)
    -- INSERT INTO audit_logs (action, admin_id, target_email, timestamp) 
    -- VALUES ('password_update', admin_user_id, target_user_email, NOW());

    RETURN jsonb_build_object(
        'success', true,
        'message', 'Authorization successful'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_all_users_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_update_user_password(TEXT, UUID) TO authenticated;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION get_all_users_admin() IS 'Returns all users for admin/manager access, bypassing RLS policies';
COMMENT ON FUNCTION check_email_exists(TEXT) IS 'Checks if an email address already exists in the users table';
COMMENT ON FUNCTION admin_update_user_password(TEXT, UUID) IS 'Validates admin permissions for password updates';
