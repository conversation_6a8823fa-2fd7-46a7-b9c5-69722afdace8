/**
 * User Registration Service for YalaOffice
 * Handles user creation with Supabase Auth and bypasses RLS restrictions
 */

import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

// Type definitions
type UserInsert = Database['public']['Tables']['users']['Insert'];
type CustomerProfileInsert = Database['public']['Tables']['customer_profiles']['Insert'];

export interface UserRegistrationData {
  email: string;
  password: string;
  fullName: string;
  userType: 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
  phone?: string;
  city?: string;
  isCompany?: boolean;
  companyName?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  iceNumber?: string;
  taxId?: string;
  legalForm?: string;
  // Customer profile data
  discountRate?: number;
  creditLimit?: number;
}

export class UserRegistrationService {
  private static instance: UserRegistrationService;

  private constructor() {}

  static getInstance(): UserRegistrationService {
    if (!UserRegistrationService.instance) {
      UserRegistrationService.instance = new UserRegistrationService();
    }
    return UserRegistrationService.instance;
  }

  /**
   * Register a new user with Supabase Auth and create profile
   */
  async registerUser(userData: UserRegistrationData): Promise<{
    user: any;
    profile: any;
    customerProfile?: any;
    error?: string;
  }> {
    try {
      console.log('🔐 Starting user registration for:', userData.email);

      // Step 1: Create user with Supabase Auth
      // Get the correct redirect URL based on environment
      const getRedirectUrl = () => {
        // In production, always use the production domain
        if (import.meta.env.VITE_APP_ENV === 'production' || window.location.hostname === 'yalaoffice.com') {
          return 'https://yalaoffice.com/auth/callback';
        }
        // In development, use the current origin
        return `${window.location.origin}/auth/callback`;
      };

      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          emailRedirectTo: getRedirectUrl(),
          data: {
            full_name: userData.fullName,
            user_type: userData.userType,
            phone: userData.phone,
            city: userData.city
          }
        }
      });

      if (authError) {
        console.error('❌ Auth registration error:', authError);
        return { user: null, profile: null, error: authError.message };
      }

      if (!authData.user) {
        return { user: null, profile: null, error: 'User creation failed' };
      }

      console.log('✅ Auth user created:', authData.user.id);

      // Step 2: Create user profile in users table
      // We'll use the service role key for this to bypass RLS
      const userProfile: UserInsert = {
        id: authData.user.id,
        email: userData.email,
        full_name: userData.fullName,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city || 'Tetouan',
        is_active: true,
        is_company: userData.isCompany || false,
        company_name: userData.companyName,
        company_address: userData.companyAddress,
        company_phone: userData.companyPhone,
        company_email: userData.companyEmail,
        ice_number: userData.iceNumber,
        tax_id: userData.taxId,
        legal_form: userData.legalForm
      };

      // For now, we'll try with the regular client
      // In production, you'd use the service role key
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .insert([userProfile])
        .select()
        .single();

      if (profileError) {
        console.error('❌ Profile creation error:', profileError);
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        return { user: null, profile: null, error: `Profile creation failed: ${profileError.message}` };
      }

      console.log('✅ User profile created:', profileData.id);

      // Step 3: Create customer profile if user is client or reseller
      let customerProfileData = null;
      if (userData.userType === 'client' || userData.userType === 'reseller') {
        const customerProfile: CustomerProfileInsert = {
          user_id: authData.user.id,
          discount_rate: userData.discountRate || 0,
          credit_limit: userData.creditLimit || 1000,
          total_orders: 0,
          total_spent: 0,
          loyalty_points: 0,
          status: 'active'
        };

        const { data: custProfileData, error: custProfileError } = await supabase
          .from('customer_profiles')
          .insert([customerProfile])
          .select()
          .single();

        if (custProfileError) {
          console.error('⚠️ Customer profile creation error:', custProfileError);
          // Don't fail the whole registration for this
        } else {
          customerProfileData = custProfileData;
          console.log('✅ Customer profile created:', custProfileData.id);
        }
      }

      return {
        user: authData.user,
        profile: profileData,
        customerProfile: customerProfileData
      };

    } catch (error: any) {
      console.error('❌ Registration service error:', error);
      return { user: null, profile: null, error: error.message };
    }
  }

  /**
   * Create multiple users from predefined data
   */
  async createPredefinedUsers(): Promise<{
    success: number;
    failed: number;
    results: any[];
  }> {
    const predefinedUsers: UserRegistrationData[] = [
      // Admin Users
      {
        email: '<EMAIL>',
        password: 'YalaAdmin2024!',
        fullName: 'Youssef El Mansouri',
        userType: 'admin',
        phone: '+212 6 12 34 56 78',
        city: 'Tetouan'
      },
      {
        email: '<EMAIL>',
        password: 'YalaAdmin2024!',
        fullName: 'Aicha Benali',
        userType: 'admin',
        phone: '+212 6 87 65 43 21',
        city: 'Tetouan'
      },
      // Manager Users
      {
        email: '<EMAIL>',
        password: 'YalaManager2024!',
        fullName: 'Omar Tazi',
        userType: 'manager',
        phone: '+212 6 11 22 33 44',
        city: 'Tetouan'
      },
      {
        email: '<EMAIL>',
        password: 'YalaManager2024!',
        fullName: 'Fatima Alaoui',
        userType: 'manager',
        phone: '+212 6 55 44 33 22',
        city: 'Tetouan'
      },
      // Client Users
      {
        email: '<EMAIL>',
        password: 'Client2024!',
        fullName: 'Ahmed Bennani',
        userType: 'client',
        phone: '+212 6 77 88 99 00',
        city: 'Tetouan',
        creditLimit: 1200
      },
      {
        email: '<EMAIL>',
        password: 'Client2024!',
        fullName: 'Khadija Amrani',
        userType: 'client',
        phone: '+212 6 33 44 55 66',
        city: 'Tetouan',
        creditLimit: 1500
      },
      {
        email: '<EMAIL>',
        password: 'Client2024!',
        fullName: 'Hassan Idrissi',
        userType: 'client',
        phone: '+212 6 99 88 77 66',
        city: 'Tetouan',
        creditLimit: 800
      },
      {
        email: '<EMAIL>',
        password: 'Client2024!',
        fullName: 'Nadia Berrada',
        userType: 'client',
        phone: '+212 6 44 55 66 77',
        city: 'Tetouan',
        creditLimit: 1000
      },
      // Reseller Users
      {
        email: '<EMAIL>',
        password: 'Reseller2024!',
        fullName: 'Rachid Fassi',
        userType: 'reseller',
        phone: '+212 6 88 99 00 11',
        city: 'Tetouan',
        isCompany: true,
        companyName: 'Papeterie Moderne SARL',
        companyAddress: 'Avenue Mohammed V, Quartier Administratif, Tetouan',
        companyPhone: '+212 5 39 12 34 56',
        companyEmail: '<EMAIL>',
        discountRate: 15,
        creditLimit: 10000
      },
      {
        email: '<EMAIL>',
        password: 'Reseller2024!',
        fullName: 'Laila Cherkaoui',
        userType: 'reseller',
        phone: '+212 6 22 33 44 55',
        city: 'Tetouan',
        isCompany: true,
        companyName: 'Fournitures du Nord',
        companyAddress: 'Rue Al Massira, Centre Ville, Tetouan',
        companyPhone: '+212 5 39 87 65 43',
        companyEmail: '<EMAIL>',
        discountRate: 12,
        creditLimit: 8000
      },
      {
        email: '<EMAIL>',
        password: 'Reseller2024!',
        fullName: 'Mohamed Alami',
        userType: 'reseller',
        phone: '+212 6 11 00 99 88',
        city: 'Tetouan',
        isCompany: true,
        companyName: 'École Plus Distribution',
        companyAddress: 'Boulevard Hassan II, Tetouan',
        companyPhone: '+212 5 39 66 77 88',
        companyEmail: '<EMAIL>',
        discountRate: 18,
        creditLimit: 15000
      },
      {
        email: '<EMAIL>',
        password: 'Reseller2024!',
        fullName: 'Samira Benali',
        userType: 'reseller',
        phone: '+212 6 77 66 55 44',
        city: 'Tetouan',
        isCompany: true,
        companyName: 'Bureau Express',
        companyAddress: 'Rue Moulay Abbas, Tetouan',
        companyPhone: '+212 5 39 44 55 66',
        companyEmail: '<EMAIL>',
        discountRate: 10,
        creditLimit: 6000
      },
      // Delivery Personnel
      {
        email: '<EMAIL>',
        password: 'Delivery2024!',
        fullName: 'Abdelkader Zouani',
        userType: 'delivery_person',
        phone: '+212 6 55 44 33 22',
        city: 'Tetouan'
      },
      {
        email: '<EMAIL>',
        password: 'Delivery2024!',
        fullName: 'Mustapha Rifi',
        userType: 'delivery_person',
        phone: '+212 6 66 77 88 99',
        city: 'Tetouan'
      }
    ];

    const results = [];
    let success = 0;
    let failed = 0;

    console.log(`🚀 Creating ${predefinedUsers.length} predefined users...`);

    for (const userData of predefinedUsers) {
      try {
        const result = await this.registerUser(userData);
        if (result.error) {
          console.log(`❌ Failed to create ${userData.email}: ${result.error}`);
          failed++;
        } else {
          console.log(`✅ Created ${userData.email} (${userData.userType})`);
          success++;
        }
        results.push({ email: userData.email, ...result });
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.log(`❌ Error creating ${userData.email}:`, error);
        failed++;
        results.push({ email: userData.email, error: error.message });
      }
    }

    return { success, failed, results };
  }
}

// Export singleton instance
export const userRegistrationService = UserRegistrationService.getInstance();
export default userRegistrationService;
