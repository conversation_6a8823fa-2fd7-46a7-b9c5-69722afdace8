/**
 * Email Confirmation Service
 * Handles email confirmation flow and user synchronization
 */

import { supabase } from '../integrations/supabase/client';
import { liveDataService } from './liveDataService';

export interface EmailConfirmationResult {
  success: boolean;
  user?: any;
  error?: string;
  requiresConfirmation?: boolean;
}

class EmailConfirmationService {
  /**
   * Check if the current URL contains an email confirmation token
   */
  isEmailConfirmationCallback(): boolean {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const type = urlParams.get('type');
    
    return !!(token && type === 'signup');
  }

  /**
   * Handle email confirmation callback
   */
  async handleEmailConfirmation(): Promise<EmailConfirmationResult> {
    try {
      console.log('🔐 Handling email confirmation callback...');
      
      // Get the current session after confirmation
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('❌ Session error during confirmation:', sessionError);
        return {
          success: false,
          error: 'Failed to confirm email. Please try again.'
        };
      }

      if (!session || !session.user) {
        console.error('❌ No session found after confirmation');
        return {
          success: false,
          error: 'Email confirmation failed. Please try registering again.'
        };
      }

      console.log('✅ Email confirmed for user:', session.user.id);

      // Wait a moment for the database trigger to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Fetch user data from the users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (userError || !userData) {
        console.error('❌ Failed to fetch user data after confirmation:', userError);
        
        // Try to create the user profile manually if it doesn't exist
        const createResult = await this.createUserProfileAfterConfirmation(session.user);
        if (!createResult.success) {
          return {
            success: false,
            error: 'Failed to create user profile. Please contact support.'
          };
        }
        
        // Fetch again after manual creation
        const { data: retryUserData, error: retryError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();
          
        if (retryError || !retryUserData) {
          return {
            success: false,
            error: 'Failed to complete registration. Please contact support.'
          };
        }
        
        return {
          success: true,
          user: this.formatUserData(retryUserData, session.user)
        };
      }

      console.log('✅ User data retrieved after confirmation:', userData);

      return {
        success: true,
        user: this.formatUserData(userData, session.user)
      };

    } catch (error: any) {
      console.error('❌ Email confirmation error:', error);
      return {
        success: false,
        error: error.message || 'Email confirmation failed'
      };
    }
  }

  /**
   * Create user profile manually after email confirmation
   */
  private async createUserProfileAfterConfirmation(authUser: any): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 Creating user profile manually after confirmation...');
      
      const userMetadata = authUser.user_metadata || {};
      
      // Insert user data
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: authUser.id,
          email: authUser.email,
          full_name: userMetadata.full_name || authUser.email,
          user_type: userMetadata.user_type || 'client',
          phone: userMetadata.phone,
          city: userMetadata.city || 'Tetouan',
          is_active: true
        });

      if (insertError) {
        console.error('❌ Failed to insert user profile:', insertError);
        return { success: false, error: insertError.message };
      }

      // Create customer profile for client/reseller users
      const userType = userMetadata.user_type || 'client';
      if (userType === 'client' || userType === 'reseller') {
        const { error: customerError } = await supabase
          .from('customer_profiles')
          .insert({
            user_id: authUser.id,
            discount_rate: userType === 'reseller' ? 5.0 : 0.0,
            credit_limit: 0,
            status: 'active'
          });

        if (customerError) {
          console.warn('⚠️ Failed to create customer profile:', customerError);
          // Don't fail the whole process for this
        }
      }

      console.log('✅ User profile created manually');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating user profile manually:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Format user data for the application
   */
  private formatUserData(userData: any, authUser: any): any {
    return {
      id: userData.id,
      fullName: userData.full_name,
      email: userData.email,
      phone: userData.phone,
      city: userData.city,
      userType: userData.user_type,
      isAuthenticated: true,
      isActive: userData.is_active,
      isCompany: userData.is_company || false,
      companyName: userData.company_name,
      companyAddress: userData.company_address,
      companyPhone: userData.company_phone,
      companyEmail: userData.company_email,
      iceNumber: userData.ice_number,
      taxId: userData.tax_id,
      legalForm: userData.legal_form,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      permissions: this.getUserPermissions(userData.user_type)
    };
  }

  /**
   * Get user permissions based on user type
   */
  private getUserPermissions(userType: string): string[] {
    const permissions: Record<string, string[]> = {
      admin: ['all'],
      manager: ['products', 'orders', 'customers', 'reports'],
      client: ['orders', 'profile'],
      reseller: ['orders', 'profile', 'bulk_orders'],
      delivery_person: ['deliveries', 'profile']
    };

    return permissions[userType] || permissions.client;
  }

  /**
   * Check if user needs email confirmation
   */
  async checkEmailConfirmationStatus(email: string): Promise<{ confirmed: boolean; exists: boolean }> {
    try {
      // Check if user exists in auth.users and is confirmed
      const { data: authUser, error } = await supabase.auth.admin.getUserByEmail(email);
      
      if (error || !authUser) {
        return { confirmed: false, exists: false };
      }

      return {
        confirmed: !!authUser.user.email_confirmed_at,
        exists: true
      };
    } catch (error) {
      console.error('Error checking email confirmation status:', error);
      return { confirmed: false, exists: false };
    }
  }

  /**
   * Resend confirmation email
   */
  async resendConfirmationEmail(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Clean up URL after email confirmation
   */
  cleanupConfirmationUrl(): void {
    // Remove confirmation parameters from URL
    const url = new URL(window.location.href);
    url.searchParams.delete('token');
    url.searchParams.delete('type');
    url.searchParams.delete('redirect_to');
    
    // Update URL without page reload
    window.history.replaceState({}, document.title, url.toString());
  }
}

export const emailConfirmationService = new EmailConfirmationService();
